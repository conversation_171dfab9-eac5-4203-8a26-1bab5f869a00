# Shery.js Setup Guide

## Overview
Shery.js has been successfully integrated into your React + Vite project using the CDN approach. This setup provides smooth animations and 3D effects without build configuration issues.

## What Was Fixed
1. **Removed npm package**: The `sheryjs` npm package was causing GLSL loader issues with Vite
2. **Added CDN scripts**: All required libraries are now loaded via CDN in `index.html`
3. **Updated components**: Components now use `window.Shery` to access the library
4. **Simplified Vite config**: Removed complex GLSL plugin configurations

## CDN Scripts Added to index.html
```html
<!-- Shery.js CSS -->
<link rel="stylesheet" href="https://unpkg.com/sheryjs/dist/Shery.css" />

<!-- Required libraries -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/0.155.0/three.min.js"></script>
<script src="https://cdn.jsdelivr.net/gh/automat/controlkit.js@master/bin/controlKit.min.js"></script>
<script type="text/javascript" src="https://unpkg.com/sheryjs/dist/Shery.js"></script>
```

## How to Use Shery.js in Components

### Basic Pattern
```jsx
import { useEffect } from 'react';

const MyComponent = () => {
  useEffect(() => {
    if (window.Shery) {
      // Initialize Shery effects here
      window.Shery.mouseFollower({
        skew: true,
        ease: "cubic-bezier(0.23, 1, 0.320, 1)",
        duration: 1,
      });
    }
  }, []);

  return (
    <div>
      {/* Your component JSX */}
    </div>
  );
};
```

## Available Effects

### 1. Mouse Follower
```jsx
window.Shery.mouseFollower({
  skew: true,
  ease: "cubic-bezier(0.23, 1, 0.320, 1)",
  duration: 1,
});
```

### 2. Text Animation
```jsx
window.Shery.textAnimate(".text-class", {
  style: 1,
  y: 10,
  delay: 0.1,
  duration: 2,
  ease: "cubic-bezier(0.23, 1, 0.320, 1)",
  multiplier: 0.1,
});
```

### 3. Magnetic Effect
```jsx
window.Shery.makeMagnet(".magnet-class", {
  ease: "cubic-bezier(0.23, 1, 0.320, 1)",
  duration: 1,
});
```

### 4. Image Effects (3D)
```jsx
window.Shery.imageEffect(".image-class", {
  style: 2, // 1-7 different styles available
  debug: false, // Set to true for debug panel
  config: {
    uFrequencyX: { value: 12, range: [0, 100] },
    uFrequencyY: { value: 12, range: [0, 100] },
    // ... more config options
  }
});
```

### 5. Image Masker
```jsx
window.Shery.imageMasker(".mask-class", {
  mouseFollower: true,
  text: "Shery",
  ease: "cubic-bezier(0.23, 1, 0.320, 1)",
  duration: 1,
});
```

### 6. Hover with Media Circle
```jsx
window.Shery.hoverWithMediaCircle(".hover-class", {
  images: ["image1.jpg", "image2.jpg", "image3.jpg"]
});
```

## Test Page
Visit `/test` to see all effects in action. This page demonstrates:
- Mouse follower effect
- Text animations
- Magnetic elements
- 3D image effects
- Image masker
- Hover media circles

## Components with Shery.js
1. **HeroSection.jsx**: Text animation, magnetic effects, and 3D image effects
2. **Showcase.jsx**: Image effects on the jam image
3. **SheryTest.jsx**: Comprehensive demo of all effects

## Tips
1. Always check `if (window.Shery)` before using effects
2. Use `useEffect` to initialize effects after component mounts
3. Add appropriate CSS classes to target elements
4. Set `debug: true` to experiment with effect parameters
5. Effects work best on images with proper aspect ratios

## Troubleshooting
- If effects don't work, check browser console for errors
- Ensure all CDN scripts are loaded before your React app
- Make sure target elements exist in the DOM when effects are initialized
- Use unique class names to avoid conflicts

## Performance
- CDN approach is more reliable than bundled approach for Shery.js
- Effects are GPU-accelerated where possible
- Consider lazy loading effects for better performance on mobile devices
