import React from 'react';
import { Suspense, useEffect } from 'react';
const Spline = React.lazy(() => import('@splinetool/react-spline'));

const HeroSection = () => {
    useEffect(() => {
        // Initialize Shery effects after component mounts
        if (window.Shery) {
            // Text animation for the main heading
            window.Shery.textAnimate(".hero-text", {
                style: 1,
                y: 10,
                duration: 0.1,
                ease: "cubic-bezier(0.23, 1, 0.320, 1)",
                multiplier: 0.1,
            });
        }
    }, []);

return (
    <div className="w-full h-screen fixed top-0 left-0 z-10">
        <Suspense fallback={<div>Loading...</div>}>
            <Spline scene="https://prod.spline.design/gmcYUMETn8KodfFJ/scene.splinecode" />
        </Suspense>
        <div className="absolute top-0 left-0 w-full h-full bg-black/20 flex justify-between">
            <div className="imgs w-1/2 h-full relative">
                    <img src="/hero-base.png" alt=""  className="absolute left-0 bottom-0 w-full h-full object-cover z-10"/>
                    <img src="/combine-product.png" alt="" className="absolute left-[15%] bottom-[12%] w-[75%] object-cover z-20 hero-ghee-image"/>
            </div>
            <div className="tagLing w-1/2 h-full flex items-center justify-center p-6 flex-col gap-8">
                    <h4 className="text-6xl text-center text-white font-['Majestic'] leading-22 tracking-wide hero-text">When people choose love and purity, they choose Rosier.</h4>
                    <p className="text-2xl font-bold bg-[#D7A72F] text-[#743506] p-4 rounded-full hero-tagline">Slow, Pure, Remembered.</p>
            </div>
        </div>
    </div>
);
};

export default HeroSection;
