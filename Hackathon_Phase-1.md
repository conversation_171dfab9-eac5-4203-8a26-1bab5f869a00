
# 🔥 Hackathon: Influencer/ Start-ups Website Build Challenge 🔥

Welcome to one of the most exciting frontend challenges you’ll ever face.  
This is your chance to apply your skills in React, design something beautiful, and make something that *feels* real.

---

## 🎯 THEME: Build a Website for an Influencer or Start-up

You’ve seen your favorite influencers and growing start-ups all over Instagram, YouTube, Twitter — but have you ever thought, what would their **personal brand or company website** look like if you designed it?

In this hackathon, your job is to **pick any Indian influencer or start-up** and **build a modern, aesthetic, and functional brand website** for them. Think of it like a real freelance project, where you’re the only developer in charge.

Be creative. Be bold. Make it feel like it belongs on the internet in 2025.

---

## 🧾 Pages You Must Include

You need to build **5 complete pages**:

1. 🏠 **Home Page**
   - Welcome section, intro, hero banner  
   - Social media links, latest content, newsletter or CTA

2. 🛍️ **Products/Services Page**
   - Showcasing the products, services, courses, or merch  
   - Must include good UI with hover, filter, or basic animations

3. 🔍 **Product/Service Detail Page**
   - Clicking on a product/service should open a dedicated page  
   - Must include image, description, price, and a call to action

4. 🔐 **Login / SignUp Page**
   - Beautiful authentication UI  
   - Optional: Hook up with Firebase or dummy login logic

5. 👤 **About Page**
   - Story of the influencer/start-up, background, achievements  
   - You can be creative with layout, timeline, or testimonials

---

## ⚙️ Tech Stack & Participation Rules

- Must be built using **React**
- You can use any CSS/JS library: Tailwind, GSAP, Framer Motion, Chakra UI, etc.
- Only **frontend** will be judged officially, but adding backend (like form handling, login logic, database) is a plus
- Project **must be responsive**
- Work solo. **No teams allowed**

---

## 📤 Submission Format

Your submission must include:

- 🏷️ **Brand/Influencer/Start-up Name** you chose  
- 💻 **GitHub Repository Link** (Mandatory)  
- 🌐 **Live Link** hosted on Netlify, Vercel, Render, etc.  
- 🎥 **Short Video (1–2 mins)** explaining your project, design process, and features (screen recording or Loom)

📩 **Submission Forms will be out on 23rd July and will close on 25th July at 11:59:59 PM sharp.**  
Make sure everything is deployed and accessible publicly before the deadline.

---

## 📅 Timeline

- 🚨 **Launch Date:** 15th July  
- 🗓️ **Submission Deadline:** 25th July (Friday)  
- 📣 **Result Announcement:** 2–3 days after submissions are closed


---

## 🏆 Prizes Worth ₹48,000+

- 🥇 **1st Prize:** ₹21,000  
- 🥈 **2nd Prize:** ₹11,000  
- 🥉 **3rd Prize:** ₹5,000  
- 🎖️ **Ranks 4th to 10th:** Get **Sheryians Discount Coupons**

Winners will also get shoutouts on Sheryians social media and Discord, plus priority for internship/mentorship opportunities.

---


## 💡 Pro Tip

This is your chance to stand out. Imagine this is your **portfolio project** or your **client demo**.  
Add those subtle details — parallax, smooth scroll, transitions, shadows, image hover effects — that make websites feel alive.

---

This hackathon isn’t just a test — it’s your opportunity to show the world what you can build.  
**Bring your A-game. Blow our minds. Let's see who rises to the top. 🚀**
