import { useGS<PERSON> } from "@gsap/react";
import gsap from "gsap";
import { useRef } from "react";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

const Showcase = () => {
  const containerRef = useRef(null);
  const leftContainerRef = useRef(null);

  useGSAP(() => {
    // Add a small delay to ensure DOM is ready
    gsap.set(leftContainerRef.current, { y: 0 }); // Reset initial position
    
    gsap.to(leftContainerRef.current, {
      y: "-200%",
      ease: "none",
      scrollTrigger: {
        trigger: containerRef.current,
        pin: true,
        start: "top top",
        end: "+=200%",
        scrub: 1,
        markers: true,
        onUpdate: (self) => {
          console.log("ScrollTrigger progress:", self.progress);
        },
        onToggle: (self) => {
          console.log("ScrollTrigger active:", self.isActive);
        }
      },
    });
  }, []);

  return (
    <div
      ref={containerRef}
      className="w-full h-screen bg-black relative z-20 flex justify-between rounded-t-[3rem] overflow-hidden"
    >
      <div className="left w-[60%] h-full relative overflow-hidden font-['abrikos'] text-white">
        <div ref={leftContainerRef} className="w-full">
          <div className="leftelem w-full h-screen p-20 flex justify-center flex-col">
            <p className="tracking-widest text-md">ROSIER FOOD</p>
            <h2 className="text-8xl my-6">Wild Forest Honey</h2>
            <div className="desc w-full tracking-wide font-[satoshi] mt-6">
              <p className="text-2xl leading-none">
                Wild Flower Honey is a multi-floral honey, responsibly collected
                from bees feeding on wild forest flowers nectar from the forest of
                the Himalayas. The honey is rich in bio-diverse vitamins,
                minerals, and amino acids boosting good health. 100% Natural |
                Ayurvedic | No added sugar.
              </p>
            </div>
          </div>
          <div className="leftelem w-full h-screen p-20 flex justify-center flex-col">
            <p className="tracking-widest text-md">ROSIER FOOD</p>
            <h2 className="text-8xl my-6">Organic Ghee</h2>
            <div className="desc w-full tracking-wide font-[satoshi] mt-6">
              <p className="text-2xl leading-none">
                Pure A2 Ghee made from grass-fed cows. Rich in vitamins and
                healthy fats. Traditional bilona method ensures maximum nutrition
                and authentic taste. 100% Natural | Chemical-free | Premium Quality.
              </p>
            </div>
          </div>
          <div className="leftelem w-full h-screen p-20 flex justify-center flex-col">
            <p className="tracking-widest text-md">ROSIER FOOD</p>
            <h2 className="text-8xl my-6">Cold Pressed Oil</h2>
            <div className="desc w-full tracking-wide font-[satoshi] mt-6">
              <p className="text-2xl leading-none">
                Traditional cold-pressed oils extracted without heat or chemicals.
                Retains natural nutrients and flavor. Perfect for cooking and
                health benefits. 100% Pure | Unrefined | Chemical-free.
              </p>
            </div>
          </div>
        </div>
      </div>
      <div className="right h-full w-[40%] relative">
        <div className="images w-full h-full">
          <img src="Honey.png" alt="" className="w-full h-full object-cover" />
        </div>
      </div>
    </div>
  );
};

export default Showcase;
