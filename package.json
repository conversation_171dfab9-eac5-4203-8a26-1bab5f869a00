{"name": "hackathons", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@gsap/react": "^2.1.2", "@splinetool/react-spline": "^4.1.0", "@splinetool/runtime": "^1.10.33", "@tailwindcss/vite": "^4.1.11", "gsap": "^3.13.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.0", "remixicon": "^4.6.0", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^5.4.10"}}